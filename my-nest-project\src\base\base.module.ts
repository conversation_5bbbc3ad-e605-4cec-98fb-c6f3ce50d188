import { Module } from '@nestjs/common';
import { BaseService } from './base.service';
import { BaseController } from './base.controller';
import { BaseEntity } from 'typeorm/repository/BaseEntity';
import { TypeOrmModule } from '@nestjs/typeorm/dist/typeorm.module';

@Module({
  imports: [TypeOrmModule.forFeature([BaseEntity])],
  controllers: [BaseController],
  providers: [BaseService],
})
export class BaseModule {}
