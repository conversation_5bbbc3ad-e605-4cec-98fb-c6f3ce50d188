"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = void 0;
class BaseService {
    repository;
    logger;
    constructor(repository, logger) {
        this.repository = repository;
        this.logger = logger;
    }
    index() {
        return this.repository.find();
    }
    findAll() {
        return this.repository.find();
    }
    findById(id) {
        const where = { id };
        return this.repository.findOne({ where });
    }
    findOne(id) {
        const where = { id };
        return this.repository.findOne({ where });
    }
    create(data) {
        return this.repository.save(data);
    }
    findByIds(ids) {
        return this.repository.findByIds(ids);
    }
    store(data) {
        return this.repository.save(data);
    }
    async update(id, data) {
        await this.repository.update(id, data);
        return this.findById(id);
    }
    delete(id) {
        return this.repository.delete(id);
    }
}
exports.BaseService = BaseService;
//# sourceMappingURL=base.service.js.map