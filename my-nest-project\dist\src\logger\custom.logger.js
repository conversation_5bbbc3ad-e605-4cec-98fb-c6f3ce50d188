"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
let LoggerService = class LoggerService {
    log(message) {
        console.log(`[LOG] ${new Date().toISOString()} - ${message}`);
    }
    error(message, trace) {
        console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
        if (trace) {
            console.error(trace);
        }
    }
    warn(message) {
        console.warn(`[WARN] ${new Date().toISOString()} - ${message}`);
    }
    debug(message) {
        console.debug(`[DEBUG] ${new Date().toISOString()} - ${message}`);
    }
    verbose(message) {
        console.info(`[VERBOSE] ${new Date().toISOString()} - ${message}`);
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)()
], LoggerService);
//# sourceMappingURL=custom.logger.js.map