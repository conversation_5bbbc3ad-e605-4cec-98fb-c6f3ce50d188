"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
const common_1 = require("@nestjs/common");
const base_service_1 = require("./base.service");
const create_base_dto_1 = require("./dto/create-base.dto");
const update_base_dto_1 = require("./dto/update-base.dto");
let BaseController = class BaseController {
    baseService;
    constructor(baseService) {
        this.baseService = baseService;
    }
    create(createBaseDto) {
        return this.baseService.create(createBaseDto);
    }
    findAll() {
        return this.baseService.findAll();
    }
    findOne(id) {
        return this.baseService.findOne(+id);
    }
    update(id, updateBaseDto) {
        return this.baseService.update(+id, updateBaseDto);
    }
    remove(id) {
        return this.baseService.delete(+id);
    }
};
exports.BaseController = BaseController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_base_dto_1.CreateBaseDto]),
    __metadata("design:returntype", void 0)
], BaseController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BaseController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BaseController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_base_dto_1.UpdateBaseDto]),
    __metadata("design:returntype", void 0)
], BaseController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BaseController.prototype, "remove", null);
exports.BaseController = BaseController = __decorate([
    (0, common_1.Controller)('base'),
    __metadata("design:paramtypes", [base_service_1.BaseService])
], BaseController);
//# sourceMappingURL=base.controller.js.map