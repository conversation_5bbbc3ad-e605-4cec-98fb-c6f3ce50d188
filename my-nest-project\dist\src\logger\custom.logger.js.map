{"version": 3, "file": "custom.logger.js", "sourceRoot": "", "sources": ["../../../src/logger/custom.logger.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAgF;AAGzE,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,GAAG,CAAC,OAAe;QACjB,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,KAAc;QACnC,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAe;QAClB,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,OAAO,CAAC,OAAe;QACrB,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE,CAAC,CAAC;IACrE,CAAC;CACF,CAAA;AAvBY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;GACA,aAAa,CAuBzB"}