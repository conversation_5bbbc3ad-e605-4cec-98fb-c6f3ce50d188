import { BaseEntity, DeleteResult, Repository } from 'typeorm';
import { IBaseService } from './i.base.service';
import { EntityId } from 'typeorm/repository/EntityId';
import { LoggerService } from 'src/logger/custom.logger';
import { HasId } from './base.interface';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
export declare class BaseService<T extends BaseEntity & HasId, R extends Repository<T>> implements IBaseService<T> {
    protected readonly repository: R;
    protected readonly logger: LoggerService;
    constructor(repository: R, logger: LoggerService);
    index(): Promise<T[]>;
    findAll(): Promise<T[]>;
    findById(id: EntityId): Promise<T>;
    findOne(id: EntityId): Promise<T>;
    create(data: any): Promise<T>;
    findByIds(ids: [EntityId]): Promise<T[]>;
    store(data: any): Promise<T>;
    update(id: EntityId, data: QueryDeepPartialEntity<T>): Promise<T>;
    delete(id: EntityId): Promise<DeleteResult>;
}
