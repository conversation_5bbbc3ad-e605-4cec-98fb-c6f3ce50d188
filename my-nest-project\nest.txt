NestJS giúp bạn tổ chức code rõ ràng hơn với:
    Module: gom các chức năng liên quan
    Controller: nhận HTTP request
    Service: xử lý logic
    DTO: validate dữ liệu
    Entity: định nghĩa bảng DB (nếu có dùng ORM)
    T : đạ<PERSON> diễn cho các kiểu dữ liệu (Model)
    R : đại diện cho các route (đường dẫn)
    Interface: định nghĩa cấu trúc của 1 đối tượng
    Type: định nghĩa kiểu dữ liệu
    Enum: định nghĩa tập hợp các giá trị
    Class: định nghĩa đối tượng
    Decorator: định nghĩa metadata cho class, property, method, parameter
    Provider: cung cấp các dịch vụ cho module
    Middleware: xử lý request trước khi đến controller

C<PERSON><PERSON> trúc chuẩn 
    src/
        ├── main.ts              # File chạy chính (bootstrap app)
        ├── app.module.ts        # Gốc của hệ thống (module root)
        ├── app.controller.ts    # Nhận request
        |── app.service.ts       # <PERSON><PERSON> lý logic
Controller là nơi nhận request → Giao cho Service xử lý → Service có thể gọi DB, xử lý dữ liệu 
→ Trả kết quả lại cho Controller → Response cho client.

🧱 3. Controller – Service – Module
    ✅ @Controller()
        Nhận request (GET, POST, PUT,...)
            @Get('hello')
            sayHello() {
            return 'Hello';
            }
✅ @Service()
Xử lý logic, được inject vào controller

    @Injectable()
        export class ProductService {
        getAll() { return [...]; }
        }
✅ @Module()
    Gom nhóm các controller và service lại
        @Module({
            controllers: [ProductController],
            providers: [ProductService]
        })_
📌 Ghi nhớ:
Controller giống người nhận đơn hàng.
Service giống nhân viên xử lý.
Module giống phòng ban gom chung lại.


✅ Tổng kết từng phần:
Thành phần	                    Vai trò
@Entity()	                Biến class thành bảng DB
@Column()	                Biến thuộc tính thành cột
@PrimaryGeneratedColumn()	Tự sinh khóa chính (id)
@Unique()	                Ràng buộc không trùng
@CreateDateColumn	        Ghi thời gian tạo bản ghi
@Exclude()	                Ẩn trường trong JSON
@Expose() + get	            Tạo trường tính toán trả ra JSON
constructor(partial)	    Cho phép khởi tạo nhanh object
