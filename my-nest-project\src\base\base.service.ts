import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeleteR<PERSON>ult,
  FindOptionsWhere,
  Repository,
} from 'typeorm';
import { IBaseService } from './i.base.service';
import { EntityId } from 'typeorm/repository/EntityId';
import { LoggerService } from 'src/logger/custom.logger';
import { HasId } from './base.interface';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

export class BaseService<T extends BaseEntity & HasId, R extends Repository<T>>
  implements IBaseService<T>
{
  protected readonly repository: R;
  protected readonly logger: LoggerService;

  constructor(repository: R, logger: LoggerService) {
    this.repository = repository;
    this.logger = logger;
  }

  index(): Promise<T[]> {
    return this.repository.find();
  }
  findAll(): Promise<T[]> {
    return this.repository.find();
  }
  findById(id: EntityId): Promise<T> {
    const where = { id } as FindOptionsWhere<T>;
    return this.repository.findOne({ where });
  }
  findOne(id: EntityId): Promise<T> {
    const where = { id } as FindOptionsWhere<T>;
    return this.repository.findOne({ where });
  }
  create(data: any): Promise<T> {
    return this.repository.save(data);
  }

  findByIds(ids: [EntityId]): Promise<T[]> {
    return this.repository.findByIds(ids);
  }

  store(data: any): Promise<T> {
    return this.repository.save(data);
  }

  async update(id: EntityId, data: QueryDeepPartialEntity<T>): Promise<T> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  delete(id: EntityId): Promise<DeleteResult> {
    return this.repository.delete(id);
  }
}
