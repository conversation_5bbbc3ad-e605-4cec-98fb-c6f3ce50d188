import { CustomLogger } from './src/logger/custom.logger';

// Test logger trong môi trường development
console.log('=== TESTING CUSTOM LOGGER ===\n');

// Tạo logger instance
const logger = new CustomLogger('TestContext');

console.log('1. Testing LOG level:');
logger.log('This is a log message');

console.log('\n2. Testing ERROR level:');
logger.error('This is an error message');
logger.error('Error with metadata', { code: 500, details: 'Something went wrong' });

console.log('\n3. Testing WARN level:');
logger.warn('This is a warning message');

console.log('\n4. Testing DEBUG level:');
logger.debug('This is a debug message');

console.log('\n5. Testing VERBOSE level:');
logger.verbose('This is a verbose message');

console.log('\n6. Testing with different context:');
logger.log('Message with custom context', 'CustomContext');

// Test trong production mode
console.log('\n=== TESTING PRODUCTION MODE ===');
process.env.NODE_ENV = 'production';
const prodLogger = new CustomLogger('ProdContext');

console.log('\n7. Production JSON logs:');
prodLogger.log('Production log message');
prodLogger.error('Production error message');
prodLogger.warn('Production warning message');
