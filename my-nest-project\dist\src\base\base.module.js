"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseModule = void 0;
const common_1 = require("@nestjs/common");
const base_service_1 = require("./base.service");
const base_controller_1 = require("./base.controller");
const BaseEntity_1 = require("typeorm/repository/BaseEntity");
const typeorm_module_1 = require("@nestjs/typeorm/dist/typeorm.module");
let BaseModule = class BaseModule {
};
exports.BaseModule = BaseModule;
exports.BaseModule = BaseModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_module_1.TypeOrmModule.forFeature([BaseEntity_1.BaseEntity])],
        controllers: [base_controller_1.BaseController],
        providers: [base_service_1.BaseService],
    })
], BaseModule);
//# sourceMappingURL=base.module.js.map