import { BaseService } from './base.service';
import { CreateBaseDto } from './dto/create-base.dto';
import { UpdateBaseDto } from './dto/update-base.dto';
export declare class BaseController {
    private readonly baseService;
    constructor(baseService: BaseService<any, any>);
    create(createBaseDto: CreateBaseDto): Promise<any>;
    findAll(): Promise<any[]>;
    findOne(id: string): Promise<any>;
    update(id: string, updateBaseDto: UpdateBaseDto): Promise<any>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
